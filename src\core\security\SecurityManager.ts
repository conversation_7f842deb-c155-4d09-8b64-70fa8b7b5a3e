/**
 * Main Security Manager implementation
 * Provides comprehensive security features including encryption, secure storage, and policy enforcement
 */

import * as crypto from 'crypto';
import * as path from 'path';
import {
  SecurityConfig,
  EncryptionContext,
  EncryptedData,
  SecurityContext,
  SecurityError,
  SecurityAuditEvent,
  HashOptions,
  TokenOptions,
  DEFAULT_SECURITY_CONFIG
} from './types';
import { AESGCMEncryptionService, EncryptionUtils } from './Encryption';
import { FileSecureKeyStore, SecureDataStorage } from './SecureStorage';
import { LoggerFactory } from '../logger';

const logger = LoggerFactory.getInstance().getLogger('SecurityManager');

export class SecurityManager {
  private config: SecurityConfig;
  private encryptionService: AESGCMEncryptionService;
  private keyStore: FileSecureKeyStore;
  private secureStorage: SecureDataStorage;
  private masterKey: Buffer | null = null;
  private auditEvents: SecurityAuditEvent[] = [];
  private isInitialized: boolean = false;

  constructor(config: Partial<SecurityConfig> = {}) {
    this.config = { ...DEFAULT_SECURITY_CONFIG, ...config };
    this.encryptionService = new AESGCMEncryptionService();
    
    // Initialize storage paths
    const securityPath = path.join(process.cwd(), 'security');
    this.keyStore = new FileSecureKeyStore(path.join(securityPath, 'keys'));
    this.secureStorage = new SecureDataStorage(path.join(securityPath, 'data'));
  }

  /**
   * Initialize the security manager
   */
  async initialize(masterPassword?: string): Promise<void> {
    try {
      logger.info('Initializing SecurityManager');

      // Initialize or load master key
      if (masterPassword) {
        this.masterKey = await this.deriveMasterKey(masterPassword);
      } else {
        this.masterKey = await this.loadOrGenerateMasterKey();
      }

      // Initialize secure storage
      await this.secureStorage.initialize(this.masterKey);

      this.isInitialized = true;
      
      await this.auditLog({
        type: 'authentication',
        operation: 'security_manager_init',
        success: true,
        severity: 'info'
      });

      logger.info('SecurityManager initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize SecurityManager', toError(error));
      
      await this.auditLog({
        type: 'authentication',
        operation: 'security_manager_init',
        success: false,
        severity: 'critical',
        details: { error: error instanceof Error ? error.message : String(error) }
      });
      
      throw error;
    }
  }

  /**
   * Encrypt data with optional context
   */
  async encrypt(data: string | Buffer, context?: EncryptionContext): Promise<EncryptedData> {
    this.ensureInitialized();

    try {
      const result = await this.encryptionService.encrypt(data, this.masterKey!, context);
      
      await this.auditLog({
        type: 'encryption',
        operation: 'encrypt_data',
        success: true,
        severity: 'info',
        details: { 
          algorithm: result.algorithm,
          context: context?.purpose,
          sensitivity: context?.sensitivity 
        }
      });

      return result;
    } catch (error) {
      await this.auditLog({
        type: 'encryption',
        operation: 'encrypt_data',
        success: false,
        severity: 'error',
        details: { error: error instanceof Error ? error.message : String(error) }
      });
      
      throw error;
    }
  }

  /**
   * Decrypt data
   */
  async decrypt(encryptedData: EncryptedData, context?: EncryptionContext): Promise<string | Buffer> {
    this.ensureInitialized();

    try {
      const result = await this.encryptionService.decrypt(encryptedData, this.masterKey!);
      
      await this.auditLog({
        type: 'decryption',
        operation: 'decrypt_data',
        success: true,
        severity: 'info',
        details: { 
          algorithm: encryptedData.algorithm,
          keyId: encryptedData.metadata.keyId 
        }
      });

      return result;
    } catch (error) {
      await this.auditLog({
        type: 'decryption',
        operation: 'decrypt_data',
        success: false,
        severity: 'error',
        details: { error: error instanceof Error ? error.message : String(error) }
      });
      
      throw error;
    }
  }

  /**
   * Store data securely
   */
  async secureStore(key: string, value: any): Promise<void> {
    this.ensureInitialized();

    try {
      await this.secureStorage.store(key, value, { encrypt: true });
      
      await this.auditLog({
        type: 'data_access',
        operation: 'secure_store',
        success: true,
        severity: 'info',
        resource: key
      });
    } catch (error) {
      await this.auditLog({
        type: 'data_access',
        operation: 'secure_store',
        success: false,
        severity: 'error',
        resource: key,
        details: { error: error instanceof Error ? error.message : String(error) }
      });
      
      throw error;
    }
  }

  /**
   * Retrieve data securely
   */
  async secureRetrieve(key: string): Promise<any> {
    this.ensureInitialized();

    try {
      const result = await this.secureStorage.retrieve(key);
      
      await this.auditLog({
        type: 'data_access',
        operation: 'secure_retrieve',
        success: true,
        severity: 'info',
        resource: key
      });

      return result;
    } catch (error) {
      await this.auditLog({
        type: 'data_access',
        operation: 'secure_retrieve',
        success: false,
        severity: 'error',
        resource: key,
        details: { error: error instanceof Error ? error.message : String(error) }
      });
      
      throw error;
    }
  }

  /**
   * Sanitize data by removing sensitive information
   */
  async sanitizeData(data: any): Promise<any> {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const sanitized = Array.isArray(data) ? [...data] : { ...data };

    // Remove common sensitive fields
    const sensitiveFields = [
      'password', 'apiKey', 'token', 'secret', 'key',
      'email', 'phone', 'ssn', 'creditCard', 'bankAccount'
    ];

    const sanitizeObject = (obj: any): any => {
      if (!obj || typeof obj !== 'object') return obj;

      if (Array.isArray(obj)) {
        return obj.map(sanitizeObject);
      }

      const result: any = {};
      for (const [key, value] of Object.entries(obj)) {
        const lowerKey = key.toLowerCase();
        
        if (sensitiveFields.some(field => lowerKey.includes(field))) {
          result[key] = '[REDACTED]';
        } else if (typeof value === 'object') {
          result[key] = sanitizeObject(value);
        } else {
          result[key] = value;
        }
      }
      
      return result;
    };

    return sanitizeObject(sanitized);
  }

  /**
   * Generate a secure random token
   */
  generateSecureToken(options: TokenOptions = {}): string {
    const length = options.length || 32;
    const charset = this.getCharset(options.charset || 'alphanumeric', options.customCharset);
    
    let token = '';
    for (let i = 0; i < length; i++) {
      const randomIndex = crypto.randomInt(0, charset.length);
      token += charset[randomIndex];
    }

    const prefix = options.prefix || '';
    const suffix = options.suffix || '';
    
    return `${prefix}${token}${suffix}`;
  }

  /**
   * Hash a password securely
   */
  async hashPassword(password: string, options: HashOptions = { algorithm: 'bcrypt' }): Promise<string> {
    const algorithm = options.algorithm || 'bcrypt';
    
    try {
      switch (algorithm) {
        case 'bcrypt':
          // Note: In a real implementation, you'd use the bcrypt library
          // For now, we'll use a secure alternative with PBKDF2
          const salt = options.salt || crypto.randomBytes(16);
          const saltBuffer = typeof salt === 'string' ? Buffer.from(salt, 'hex') : salt;
          const iterations = options.iterations || 100000;
          const hash = crypto.pbkdf2Sync(password, saltBuffer, iterations, 64, 'sha256');
          return `pbkdf2:${iterations}:${saltBuffer.toString('hex')}:${hash.toString('hex')}`;
          
        case 'sha256':
        case 'sha512':
          const hashAlg = crypto.createHash(algorithm);
          if (options.salt) {
            const saltStr = typeof options.salt === 'string' ? options.salt : options.salt.toString('hex');
            hashAlg.update(password + saltStr);
          } else {
            hashAlg.update(password);
          }
          return hashAlg.digest('hex');
          
        default:
          throw new Error(`Unsupported hash algorithm: ${algorithm}`);
      }
    } catch (error) {
      const securityError = new Error('Password hashing failed') as SecurityError;
      securityError.code = 'HASH_FAILED';
      securityError.details = error;
      securityError.severity = 'high';
      throw securityError;
    }
  }

  /**
   * Verify a password against its hash
   */
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    try {
      if (hash.startsWith('pbkdf2:')) {
        const parts = hash.split(':');
        if (parts.length !== 4) return false;
        
        const iterations = parseInt(parts[1]);
        const salt = Buffer.from(parts[2], 'hex');
        const expectedHash = parts[3];
        
        const computedHash = crypto.pbkdf2Sync(password, salt, iterations, 64, 'sha256');
        return crypto.timingSafeEqual(Buffer.from(expectedHash, 'hex'), computedHash);
      } else {
        // Simple hash comparison (not recommended for passwords)
        const computedHash = crypto.createHash('sha256').update(password).digest('hex');
        return crypto.timingSafeEqual(Buffer.from(hash), Buffer.from(computedHash));
      }
    } catch (error) {
      logger.error('Password verification failed', toError(error));
      return false;
    }
  }

  /**
   * Check if we're in a secure context
   */
  isSecureContext(): boolean {
    return this.isInitialized && this.masterKey !== null;
  }

  /**
   * Enforce security policy for an operation
   */
  async enforceSecurityPolicy(operation: string, context: SecurityContext): Promise<boolean> {
    // Basic security policy enforcement
    // In a real implementation, this would check against configured policies
    
    await this.auditLog({
      type: 'authorization',
      operation: 'policy_check',
      success: true,
      severity: 'info',
      details: { 
        operation,
        userId: context.userId,
        resource: context.resource 
      }
    });

    return true; // Allow all operations for now
  }

  /**
   * Get security audit events
   */
  getAuditEvents(limit: number = 100): SecurityAuditEvent[] {
    return this.auditEvents.slice(-limit);
  }

  /**
   * Clear audit events
   */
  clearAuditEvents(): void {
    this.auditEvents = [];
  }

  private ensureInitialized(): void {
    if (!this.isInitialized) {
      throw new Error('SecurityManager not initialized');
    }
  }

  private async deriveMasterKey(password: string): Promise<Buffer> {
    const salt = await this.getOrCreateSalt();
    return crypto.pbkdf2Sync(password, salt, this.config.encryption.iterations, 32, 'sha256');
  }

  private async loadOrGenerateMasterKey(): Promise<Buffer> {
    const existingKey = await this.keyStore.retrieveMasterKey();
    if (existingKey) {
      return existingKey;
    }

    const newKey = crypto.randomBytes(32);
    await this.keyStore.storeMasterKey(newKey);
    return newKey;
  }

  private async getOrCreateSalt(): Promise<Buffer> {
    // In a real implementation, this would be stored securely
    return crypto.randomBytes(this.config.encryption.saltLength);
  }

  private getCharset(type: string, custom?: string): string {
    switch (type) {
      case 'alphanumeric':
        return 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      case 'hex':
        return '0123456789abcdef';
      case 'base64':
        return 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
      case 'custom':
        return custom || 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      default:
        return 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    }
  }

  private async auditLog(event: Partial<SecurityAuditEvent>): Promise<void> {
    const auditEvent: SecurityAuditEvent = {
      id: crypto.randomUUID(),
      timestamp: new Date(),
      type: event.type || 'data_access',
      severity: event.severity || 'info',
      operation: event.operation || 'unknown',
      success: event.success ?? true,
      userId: event.userId,
      sessionId: event.sessionId,
      resource: event.resource,
      details: event.details,
      ipAddress: event.ipAddress,
      userAgent: event.userAgent
    };

    this.auditEvents.push(auditEvent);

    // Keep only the last 1000 events in memory
    if (this.auditEvents.length > 1000) {
      this.auditEvents = this.auditEvents.slice(-1000);
    }

    // Log to application logger
    const logLevel = auditEvent.severity === 'critical' || auditEvent.severity === 'error' ? 'error' : 'info';
    logger[logLevel]('Security audit event', {
      eventId: auditEvent.id,
      type: auditEvent.type,
      operation: auditEvent.operation,
      success: auditEvent.success,
      details: auditEvent.details
    });
  }
}
