/**
 * Main Context Extractor implementation
 * Screen capture, OCR, clipboard monitoring, and context gathering capabilities
 */

import { EventEmitter } from 'events';
import {
  IContextExtractor,
  ContextExtractorConfig,
  ScreenCaptureOptions,
  ScreenRegion,
  CaptureResult,
  WindowInfo,
  SystemInfo,
  ContextExtractorError,
  ScreenCaptureError,
  FileReadError,
  WebExtractionError
} from './types';
import { ScreenCaptureService } from './ScreenCaptureService';
import { OCREngineFactory } from './OCREngineFactory';
import { ClipboardMonitor } from './ClipboardMonitor';
import { WindowManager } from './WindowManager';
import { FileContentReader } from './FileContentReader';
import { WebContentExtractor } from './WebContentExtractor';
import { ContextProcessor } from './ContextProcessor';
import { LoggerFactory } from '../../core/logger';
import { toError } from '../../core/utils/errorUtils';

const logger = LoggerFactory.getInstance().getLogger('ContextExtractor');

/**
 * Main Context Extractor implementation
 */
export class ContextExtractor extends EventEmitter implements IContextExtractor {
  private config: ContextExtractorConfig;
  private screenCapture!: ScreenCaptureService;
  private ocrEngine!: any;
  private clipboardMonitor!: ClipboardMonitor;
  private windowManager!: WindowManager;
  private fileReader!: FileContentReader;
  private webExtractor!: WebContentExtractor;
  private contextProcessor!: ContextProcessor;
  private isInitialized: boolean = false;
  private metrics!: {
    totalCaptures: number;
    totalOCROperations: number;
    totalClipboardReads: number;
    totalFileReads: number;
    totalCaptureTime: number;
    totalOCRTime: number;
    ocrSuccesses: number;
    startTime: Date;
  };

  constructor(config?: Partial<ContextExtractorConfig>) {
    super();
    
    this.config = {
      ocr: {
        engine: 'tesseract',
        defaultLanguage: 'eng',
        confidence: 0.7,
        enableCache: true,
        ...config?.ocr
      },
      clipboard: {
        monitoringEnabled: false,
        monitoringInterval: 1000,
        historySize: 100,
        enableHistory: true,
        ...config?.clipboard
      },
      screen: {
        defaultFormat: 'png',
        defaultQuality: 90,
        tempDirectory: '',
        autoCleanup: true,
        ...config?.screen
      },
      files: {
        maxFileSize: 10 * 1024 * 1024, // 10MB
        supportedExtensions: ['.txt', '.md', '.json', '.js', '.ts', '.py', '.html', '.css'],
        encoding: 'utf-8',
        ...config?.files
      },
      web: {
        timeout: 30000,
        userAgent: 'ContextExtractor/1.0',
        enableJavaScript: false,
        ...config?.web
      }
    };

    this.initializeComponents();
    this.initializeMetrics();
  }

  /**
   * Initialize all components
   */
  private initializeComponents(): void {
    this.screenCapture = new ScreenCaptureService(this.config.screen);
    this.ocrEngine = OCREngineFactory.create(this.config.ocr.engine, this.config.ocr);
    this.clipboardMonitor = new ClipboardMonitor(this.config.clipboard);
    this.windowManager = new WindowManager();
    this.fileReader = new FileContentReader(this.config.files);
    this.webExtractor = new WebContentExtractor(this.config.web);
    this.contextProcessor = new ContextProcessor();
  }

  /**
   * Initialize metrics tracking
   */
  private initializeMetrics(): void {
    this.metrics = {
      totalCaptures: 0,
      totalOCROperations: 0,
      totalClipboardReads: 0,
      totalFileReads: 0,
      totalCaptureTime: 0,
      totalOCRTime: 0,
      ocrSuccesses: 0,
      startTime: new Date()
    };
  }

  /**
   * Initialize the context extractor
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      await this.ocrEngine.initialize();
      
      if (this.config.clipboard.monitoringEnabled) {
        this.clipboardMonitor.startMonitoring(this.config.clipboard.monitoringInterval);
      }

      this.isInitialized = true;
      logger.info('Context extractor initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize context extractor', { error });
      throw new ContextExtractorError('Failed to initialize context extractor', toError(error));
    }
  }

  /**
   * Capture full screen
   */
  async captureScreen(options: ScreenCaptureOptions = {}): Promise<CaptureResult> {
    const startTime = Date.now();
    
    try {
      this.emit('capture-start', { type: 'screen', options });

      const captureOptions = {
        format: this.config.screen.defaultFormat,
        quality: this.config.screen.defaultQuality,
        includeOCR: true,
        ocrLanguage: this.config.ocr.defaultLanguage,
        ...options
      };

      const result = await this.screenCapture.captureFullScreen(captureOptions);

      // Perform OCR if requested
      if (captureOptions.includeOCR && result.imagePath) {
        const ocrStartTime = Date.now();
        try {
          result.extractedText = await this.ocrEngine.extractText(result.imagePath, {
            language: captureOptions.ocrLanguage,
            confidence: this.config.ocr.confidence
          });
          
          this.metrics.totalOCROperations++;
          this.metrics.totalOCRTime += Date.now() - ocrStartTime;
          this.metrics.ocrSuccesses++;
          
          this.emit('ocr-complete', { 
            text: result.extractedText,
            confidence: this.config.ocr.confidence
          });
        } catch (ocrError) {
          logger.warn('OCR extraction failed', { error: ocrError });
          result.extractedText = '';
        }
      }

      // Update metrics
      this.metrics.totalCaptures++;
      this.metrics.totalCaptureTime += Date.now() - startTime;

      this.emit('capture-complete', { 
        type: 'screen',
        result,
        processingTime: Date.now() - startTime
      });

      logger.info('Screen captured successfully', { 
        hasText: !!result.extractedText,
        textLength: result.extractedText?.length || 0,
        processingTime: Date.now() - startTime
      });

      return result;
    } catch (error) {
      logger.error('Failed to capture screen', { error, options });
      this.emit('error', new ScreenCaptureError('Failed to capture screen', toError(error)));
      throw error;
    }
  }

  /**
   * Capture specific region
   */
  async captureRegion(region: ScreenRegion, options: ScreenCaptureOptions = {}): Promise<CaptureResult> {
    const startTime = Date.now();
    
    try {
      this.emit('capture-start', { type: 'region', region, options });

      const captureOptions = {
        format: this.config.screen.defaultFormat,
        quality: this.config.screen.defaultQuality,
        includeOCR: true,
        ocrLanguage: this.config.ocr.defaultLanguage,
        ...options,
        region
      };

      const result = await this.screenCapture.captureRegion(region, captureOptions);

      // Perform OCR if requested
      if (captureOptions.includeOCR && result.imagePath) {
        try {
          result.extractedText = await this.ocrEngine.extractText(result.imagePath, {
            language: captureOptions.ocrLanguage,
            confidence: this.config.ocr.confidence
          });
          
          this.metrics.totalOCROperations++;
          this.metrics.ocrSuccesses++;
        } catch (ocrError) {
          logger.warn('OCR extraction failed for region', { error: ocrError });
          result.extractedText = '';
        }
      }

      this.metrics.totalCaptures++;
      this.metrics.totalCaptureTime += Date.now() - startTime;

      this.emit('capture-complete', { 
        type: 'region',
        result,
        processingTime: Date.now() - startTime
      });

      logger.info('Region captured successfully', { 
        region,
        hasText: !!result.extractedText,
        processingTime: Date.now() - startTime
      });

      return result;
    } catch (error) {
      logger.error('Failed to capture region', { error, region });
      this.emit('error', new ScreenCaptureError('Failed to capture region', toError(error)));
      throw error;
    }
  }

  /**
   * Capture active window
   */
  async captureActiveWindow(): Promise<CaptureResult> {
    try {
      this.emit('capture-start', { type: 'window' });

      const result = await this.screenCapture.captureActiveWindow();

      this.metrics.totalCaptures++;

      this.emit('capture-complete', { 
        type: 'window',
        result
      });

      logger.info('Active window captured successfully');
      return result;
    } catch (error) {
      logger.error('Failed to capture active window', { error });
      this.emit('error', new ScreenCaptureError('Failed to capture active window', toError(error)));
      throw error;
    }
  }

  /**
   * Get clipboard text
   */
  async getClipboardText(): Promise<string> {
    try {
      const content = this.clipboardMonitor.getCurrentContent();
      this.metrics.totalClipboardReads++;
      
      logger.debug('Clipboard text retrieved', { length: content.length });
      return content;
    } catch (error) {
      logger.error('Failed to get clipboard text', { error });
      throw new ContextExtractorError('Failed to get clipboard text', toError(error));
    }
  }

  /**
   * Get active window text
   */
  async getActiveWindowText(): Promise<string> {
    try {
      const text = await this.windowManager.getActiveWindowText();
      
      logger.debug('Active window text retrieved', { length: text.length });
      return text;
    } catch (error) {
      logger.error('Failed to get active window text', { error });
      throw new ContextExtractorError('Failed to get active window text', toError(error));
    }
  }

  /**
   * Get active window info
   */
  async getActiveWindowInfo(): Promise<WindowInfo> {
    try {
      const info = await this.windowManager.getActiveWindow();
      if (!info) {
        throw new ContextExtractorError('No active window found');
      }
      
      logger.debug('Active window info retrieved', { title: info.title, app: info.application });
      return info;
    } catch (error) {
      logger.error('Failed to get active window info', { error });
      throw new ContextExtractorError('Failed to get active window info', toError(error));
    }
  }

  /**
   * Get selected text (placeholder implementation)
   */
  async getSelectedText(): Promise<string> {
    try {
      // This would typically use platform-specific APIs to get selected text
      // For now, we'll return clipboard content as a fallback
      const clipboardText = await this.getClipboardText();
      
      logger.debug('Selected text retrieved (from clipboard)', { length: clipboardText.length });
      return clipboardText;
    } catch (error) {
      logger.error('Failed to get selected text', { error });
      throw new ContextExtractorError('Failed to get selected text', toError(error));
    }
  }

  /**
   * Get system information
   */
  async getSystemInfo(): Promise<SystemInfo> {
    try {
      const os = await import('os');
      
      const systemInfo: SystemInfo = {
        platform: process.platform,
        version: os.release(),
        arch: process.arch,
        hostname: os.hostname(),
        username: os.userInfo().username,
        currentTime: new Date(),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        locale: Intl.DateTimeFormat().resolvedOptions().locale,
        memory: {
          total: os.totalmem(),
          free: os.freemem(),
          used: os.totalmem() - os.freemem(),
          percentage: ((os.totalmem() - os.freemem()) / os.totalmem()) * 100
        },
        cpu: {
          model: os.cpus()[0]?.model || 'Unknown',
          cores: os.cpus().length,
          usage: 0 // Would need additional calculation for real CPU usage
        }
      };
      
      logger.debug('System info retrieved');
      return systemInfo;
    } catch (error) {
      logger.error('Failed to get system info', { error });
      throw new ContextExtractorError('Failed to get system info', toError(error));
    }
  }

  /**
   * Extract content from file
   */
  async extractFromFile(filePath: string): Promise<string> {
    try {
      const content = await this.fileReader.readFile(filePath);
      this.metrics.totalFileReads++;
      
      this.emit('file-read', { filePath, contentLength: content.length });
      
      logger.info('File content extracted', { filePath, contentLength: content.length });
      return content;
    } catch (error) {
      logger.error('Failed to extract from file', { error, filePath });
      this.emit('error', new FileReadError('Failed to extract from file', toError(error)));
      throw error;
    }
  }

  /**
   * Extract content from URL
   */
  async extractFromUrl(url: string): Promise<string> {
    try {
      const webContent = await this.webExtractor.extractFromUrl(url);
      
      logger.info('Web content extracted', { url, contentLength: webContent.text.length });
      return webContent.text;
    } catch (error) {
      logger.error('Failed to extract from URL', { error, url });
      this.emit('error', new WebExtractionError('Failed to extract from URL', toError(error)));
      throw error;
    }
  }

  /**
   * Get metrics
   */
  getMetrics() {
    const uptime = Date.now() - this.metrics.startTime.getTime();
    const averageCaptureTime = this.metrics.totalCaptures > 0 ? 
      this.metrics.totalCaptureTime / this.metrics.totalCaptures : 0;
    const averageOCRTime = this.metrics.totalOCROperations > 0 ? 
      this.metrics.totalOCRTime / this.metrics.totalOCROperations : 0;
    const ocrSuccessRate = this.metrics.totalOCROperations > 0 ? 
      this.metrics.ocrSuccesses / this.metrics.totalOCROperations : 0;

    return {
      totalCaptures: this.metrics.totalCaptures,
      totalOCROperations: this.metrics.totalOCROperations,
      totalClipboardReads: this.metrics.totalClipboardReads,
      totalFileReads: this.metrics.totalFileReads,
      averageCaptureTime,
      averageOCRTime,
      ocrSuccessRate,
      uptime,
      lastResetTime: this.metrics.startTime
    };
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<ContextExtractorConfig>): void {
    this.config = { ...this.config, ...config };
    
    // Update clipboard monitoring if needed
    if (config.clipboard?.monitoringEnabled !== undefined) {
      if (config.clipboard.monitoringEnabled) {
        this.clipboardMonitor.startMonitoring(this.config.clipboard.monitoringInterval);
      } else {
        this.clipboardMonitor.stopMonitoring();
      }
    }

    logger.info('Context extractor configuration updated');
  }

  /**
   * Get current configuration
   */
  getConfig(): ContextExtractorConfig {
    return { ...this.config };
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    try {
      this.clipboardMonitor.stopMonitoring();
      await this.screenCapture.cleanup();
      this.removeAllListeners();
      
      logger.info('Context extractor cleaned up');
    } catch (error) {
      logger.error('Error during context extractor cleanup', { error });
    }
  }
}

// Global instance management
let globalContextExtractor: ContextExtractor | null = null;

/**
 * Get the global context extractor instance
 */
export const getContextExtractor = (): ContextExtractor => {
  if (!globalContextExtractor) {
    globalContextExtractor = new ContextExtractor();
  }
  return globalContextExtractor;
};

/**
 * Initialize the global context extractor instance
 */
export const initializeContextExtractor = async (config?: Partial<ContextExtractorConfig>): Promise<ContextExtractor> => {
  if (globalContextExtractor) {
    globalContextExtractor.updateConfig(config || {});
  } else {
    globalContextExtractor = new ContextExtractor(config);
  }

  await globalContextExtractor.initialize();
  return globalContextExtractor;
};


