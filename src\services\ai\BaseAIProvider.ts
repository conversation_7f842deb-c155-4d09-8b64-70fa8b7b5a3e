/**
 * Base AI Provider implementation
 * Abstract base class for AI providers with common functionality
 */

import { EventEmitter } from 'events';
import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  AIProvider, 
  AIProviderConfig, 
  AIModel, 
  AIRequest, 
  AIResponse, 
  AIStreamChunk, 
  AIUsageStats,
  AIProviderError,
  AIRateLimitError,
  AIQuotaExceededError
} from './types';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('BaseAIProvider');

/**
 * Rate limiter for API requests
 */
class RateLimiter {
  private requests: number[] = [];
  private tokens: number[] = [];

  constructor(
    private requestsPerMinute: number = 60,
    private tokensPerMinute: number = 90000
  ) {}

  canMakeRequest(estimatedTokens: number = 0): boolean {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;

    // Clean old entries
    this.requests = this.requests.filter(time => time > oneMinuteAgo);
    this.tokens = this.tokens.filter(time => time > oneMinuteAgo);

    // Check limits
    const canMakeRequest = this.requests.length < this.requestsPerMinute;
    const canUseTokens = this.tokens.length + estimatedTokens <= this.tokensPerMinute;

    return canMakeRequest && canUseTokens;
  }

  recordRequest(tokensUsed: number = 0): void {
    const now = Date.now();
    this.requests.push(now);
    
    for (let i = 0; i < tokensUsed; i++) {
      this.tokens.push(now);
    }
  }

  getWaitTime(): number {
    if (this.requests.length === 0) return 0;
    
    const oldestRequest = Math.min(...this.requests);
    const waitTime = 60000 - (Date.now() - oldestRequest);
    return Math.max(0, waitTime);
  }
}

/**
 * Abstract base AI provider class
 */
export abstract class BaseAIProvider extends EventEmitter implements IAIProvider {
  public readonly name: AIProvider;
  public readonly config: AIProviderConfig;
  
  protected rateLimiter: RateLimiter;
  protected usageStats: AIUsageStats;
  protected isInitialized: boolean = false;

  constructor(name: AIProvider, config: AIProviderConfig) {
    super();
    this.name = name;
    this.config = config;
    
    // Initialize rate limiter
    this.rateLimiter = new RateLimiter(
      config.rateLimits?.requestsPerMinute,
      config.rateLimits?.tokensPerMinute
    );

    // Initialize usage stats
    this.usageStats = {
      provider: name,
      totalRequests: 0,
      totalTokens: 0,
      totalCost: 0,
      averageLatency: 0,
      errorRate: 0,
      lastUsed: new Date(),
      dailyUsage: []
    };
  }

  /**
   * Initialize the provider
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      await this.validateConfig();
      this.isInitialized = true;
      logger.info(`AI Provider ${this.name} initialized successfully`);
    } catch (error) {
      logger.error(`Failed to initialize AI Provider ${this.name}`, toError(error));
      throw error;
    }
  }

  /**
   * Get available models
   */
  abstract getModels(): Promise<AIModel[]>;

  /**
   * Get specific model
   */
  async getModel(modelId: string): Promise<AIModel | null> {
    const models = await this.getModels();
    return models.find(model => model.id === modelId) || null;
  }

  /**
   * Complete AI request
   */
  async complete(request: AIRequest): Promise<AIResponse> {
    this.ensureInitialized();
    
    const startTime = Date.now();
    
    try {
      // Estimate tokens for rate limiting
      const estimatedTokens = await this.estimateRequestTokens(request);
      
      // Check rate limits
      if (!this.rateLimiter.canMakeRequest(estimatedTokens)) {
        const waitTime = this.rateLimiter.getWaitTime();
        throw new AIRateLimitError(this.name, waitTime);
      }

      // Make the actual request
      const response = await this.makeRequest(request);
      
      // Record usage
      this.recordUsage(response, Date.now() - startTime);
      
      // Update rate limiter
      this.rateLimiter.recordRequest(response.usage.totalTokens);
      
      logger.debug(`AI request completed`, {
        provider: this.name,
        model: response.model,
        tokens: response.usage.totalTokens,
        latency: response.latency
      });

      return response;
    } catch (error) {
      this.recordError();
      logger.error(`AI request failed`, { provider: this.name, error });
      
      if (error instanceof AIProviderError) {
        throw error;
      }
      
      throw new AIProviderError(
        `Request failed: ${error instanceof Error ? error.message : String(error)}`,
        this.name
      );
    }
  }

  /**
   * Stream AI request
   */
  async* stream(request: AIRequest): AsyncIterable<AIStreamChunk> {
    this.ensureInitialized();
    
    const startTime = Date.now();
    
    try {
      // Estimate tokens for rate limiting
      const estimatedTokens = await this.estimateRequestTokens(request);
      
      // Check rate limits
      if (!this.rateLimiter.canMakeRequest(estimatedTokens)) {
        const waitTime = this.rateLimiter.getWaitTime();
        throw new AIRateLimitError(this.name, waitTime);
      }

      // Make the streaming request
      const stream = this.makeStreamRequest(request);
      let totalTokens = 0;
      
      for await (const chunk of stream) {
        if (chunk.usage?.totalTokens) {
          totalTokens = chunk.usage.totalTokens;
        }
        yield chunk;
      }
      
      // Record usage
      this.recordStreamUsage(totalTokens, Date.now() - startTime);
      
      // Update rate limiter
      this.rateLimiter.recordRequest(totalTokens);
      
      logger.debug(`AI stream completed`, {
        provider: this.name,
        tokens: totalTokens,
        latency: Date.now() - startTime
      });
      
    } catch (error) {
      this.recordError();
      logger.error(`AI stream failed`, { provider: this.name, error });
      
      if (error instanceof AIProviderError) {
        throw error;
      }
      
      throw new AIProviderError(
        `Stream failed: ${error instanceof Error ? error.message : String(error)}`,
        this.name
      );
    }
  }

  /**
   * Estimate tokens for text
   */
  async estimateTokens(text: string, model?: string): Promise<number> {
    // Simple estimation: ~4 characters per token for most models
    // Subclasses should override with more accurate estimation
    return Math.ceil(text.length / 4);
  }

  /**
   * Validate provider configuration
   */
  async validateConfig(): Promise<boolean> {
    if (!this.config.enabled) {
      throw new AIProviderError('Provider is disabled', this.name, 'DISABLED');
    }

    if (!this.config.apiKey) {
      throw new AIProviderError('API key is required', this.name, 'MISSING_API_KEY');
    }

    return true;
  }

  /**
   * Get usage statistics
   */
  async getUsage(): Promise<AIUsageStats> {
    return { ...this.usageStats };
  }

  /**
   * Abstract methods to be implemented by subclasses
   */
  protected abstract makeRequest(request: AIRequest): Promise<AIResponse>;
  protected abstract makeStreamRequest(request: AIRequest): AsyncIterable<AIStreamChunk>;

  /**
   * Estimate tokens for a request
   */
  protected async estimateRequestTokens(request: AIRequest): Promise<number> {
    let totalTokens = 0;
    
    for (const message of request.messages) {
      totalTokens += await this.estimateTokens(message.content);
    }
    
    // Add some overhead for system tokens
    return Math.ceil(totalTokens * 1.1);
  }

  /**
   * Record successful usage
   */
  protected recordUsage(response: AIResponse, latency: number): void {
    const stats = this.usageStats as any;
    stats.totalRequests++;
    stats.totalTokens += response.usage.totalTokens;
    stats.totalCost += response.usage.cost || 0;
    stats.lastUsed = new Date();

    // Update average latency
    stats.averageLatency =
      (stats.averageLatency * (stats.totalRequests - 1) + latency) /
      stats.totalRequests;

    // Update daily usage
    this.updateDailyUsage(response.usage.totalTokens, response.usage.cost || 0);
    
    this.emit('usage', {
      provider: this.name,
      tokens: response.usage.totalTokens,
      cost: response.usage.cost,
      latency
    });
  }

  /**
   * Record streaming usage
   */
  protected recordStreamUsage(tokens: number, latency: number): void {
    const stats = this.usageStats as any;
    stats.totalRequests++;
    stats.totalTokens += tokens;
    stats.lastUsed = new Date();

    // Update average latency
    stats.averageLatency =
      (stats.averageLatency * (stats.totalRequests - 1) + latency) /
      stats.totalRequests;

    // Update daily usage
    this.updateDailyUsage(tokens, 0);
    
    this.emit('usage', {
      provider: this.name,
      tokens,
      latency
    });
  }

  /**
   * Record error
   */
  protected recordError(): void {
    const stats = this.usageStats as any;
    const totalOperations = stats.totalRequests + 1;
    const currentErrors = stats.errorRate * stats.totalRequests;
    stats.errorRate = (currentErrors + 1) / totalOperations;
    
    this.emit('error', {
      provider: this.name,
      errorRate: this.usageStats.errorRate
    });
  }

  /**
   * Update daily usage statistics
   */
  private updateDailyUsage(tokens: number, cost: number): void {
    const today = new Date().toISOString().split('T')[0];
    let todayUsage = this.usageStats.dailyUsage.find(usage => usage.date === today);
    
    if (!todayUsage) {
      todayUsage = { date: today, requests: 0, tokens: 0, cost: 0 };
      this.usageStats.dailyUsage.push(todayUsage);
    }
    
    (todayUsage as any).requests++;
    (todayUsage as any).tokens += tokens;
    (todayUsage as any).cost += cost;

    // Keep only last 30 days
    (this.usageStats as any).dailyUsage = this.usageStats.dailyUsage
      .sort((a, b) => b.date.localeCompare(a.date))
      .slice(0, 30);
  }

  /**
   * Ensure provider is initialized
   */
  protected ensureInitialized(): void {
    if (!this.isInitialized) {
      throw new AIProviderError('Provider not initialized', this.name, 'NOT_INITIALIZED');
    }
  }

  /**
   * Create HTTP headers for requests
   */
  protected createHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'PromptPilot/1.0.0',
      ...this.config.headers
    };

    if (this.config.apiKey) {
      headers['Authorization'] = `Bearer ${this.config.apiKey}`;
    }

    if (this.config.organization) {
      headers['OpenAI-Organization'] = this.config.organization;
    }

    return headers;
  }

  /**
   * Handle HTTP errors
   */
  protected handleHttpError(status: number, message: string, details?: any): never {
    switch (status) {
      case 401:
        throw new AIProviderError('Invalid API key', this.name, 'INVALID_API_KEY', status, details);
      case 402:
        throw new AIQuotaExceededError(this.name, 'cost');
      case 429:
        throw new AIRateLimitError(this.name, details?.retryAfter);
      case 404:
        throw new AIProviderError('Model not found', this.name, 'MODEL_NOT_FOUND', status, details);
      case 400:
        throw new AIProviderError(`Invalid request: ${message}`, this.name, 'INVALID_REQUEST', status, details);
      case 500:
      case 502:
      case 503:
      case 504:
        throw new AIProviderError(`Server error: ${message}`, this.name, 'SERVER_ERROR', status, details);
      default:
        throw new AIProviderError(`HTTP ${status}: ${message}`, this.name, 'HTTP_ERROR', status, details);
    }
  }
}
