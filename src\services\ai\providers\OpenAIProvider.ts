/**
 * OpenAI Provider implementation
 * Handles OpenAI API integration with streaming support
 */

import { BaseAIProvider } from '../BaseAIProvider';
import { 
  AIModel, 
  AIRequest, 
  AIResponse, 
  AIStreamChunk, 
  AIProviderConfig,
  ModelCapabilities,
  AIProviderError,
  AIMessage
} from '../types';
import { LoggerFactory } from '../../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('OpenAIProvider');

/**
 * OpenAI API response interfaces
 */
interface OpenAIModel {
  id: string;
  object: string;
  created: number;
  owned_by: string;
}

interface OpenAIMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  name?: string;
  tool_call_id?: string;
}

interface OpenAIRequest {
  model: string;
  messages: OpenAIMessage[];
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stop?: string[];
  stream?: boolean;
  tools?: any[];
  tool_choice?: any;
  response_format?: { type: 'text' | 'json_object' };
  seed?: number;
  user?: string;
}

interface OpenAIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string;
      tool_calls?: any[];
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface OpenAIStreamChunk {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    delta: {
      role?: string;
      content?: string;
      tool_calls?: any[];
    };
    finish_reason?: string;
  }[];
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

/**
 * OpenAI Provider implementation
 */
export class OpenAIProvider extends BaseAIProvider {
  private readonly baseUrl: string;
  private readonly models: Map<string, AIModel> = new Map();

  constructor(config: AIProviderConfig) {
    super('openai', config);
    this.baseUrl = config.baseUrl || 'https://api.openai.com/v1';
    this.initializeModels();
  }

  /**
   * Get available models
   */
  async getModels(): Promise<AIModel[]> {
    try {
      const response = await this.makeHttpRequest('/models', 'GET');
      const data = await response.json();
      
      const models: AIModel[] = data.data
        .filter((model: OpenAIModel) => model.id.includes('gpt'))
        .map((model: OpenAIModel) => this.mapOpenAIModel(model));

      // Update local cache
      models.forEach(model => this.models.set(model.id, model));
      
      return models;
    } catch (error) {
      logger.warn('Failed to fetch models from OpenAI, using cached models', toError(error));
      return Array.from(this.models.values());
    }
  }

  /**
   * Make AI request
   */
  protected async makeRequest(request: AIRequest): Promise<AIResponse> {
    const openaiRequest = this.convertToOpenAIRequest(request);
    
    const response = await this.makeHttpRequest('/chat/completions', 'POST', openaiRequest);
    const data: OpenAIResponse = await response.json();
    
    return this.convertFromOpenAIResponse(data, request);
  }

  /**
   * Make streaming AI request
   */
  protected async* makeStreamRequest(request: AIRequest): AsyncIterable<AIStreamChunk> {
    const openaiRequest = { ...this.convertToOpenAIRequest(request), stream: true };
    
    const response = await this.makeHttpRequest('/chat/completions', 'POST', openaiRequest);
    
    if (!response.body) {
      throw new AIProviderError('No response body for streaming request', this.name);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue;
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') return;
            
            try {
              const chunk: OpenAIStreamChunk = JSON.parse(data);
              yield this.convertFromOpenAIStreamChunk(chunk);
            } catch (error) {
              logger.warn('Failed to parse stream chunk', { data, error });
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * Estimate tokens using OpenAI's tiktoken-like estimation
   */
  async estimateTokens(text: string, model?: string): Promise<number> {
    // More accurate estimation for OpenAI models
    // This is a simplified version - in production, use tiktoken library
    const avgCharsPerToken = model?.includes('gpt-4') ? 3.5 : 4;
    return Math.ceil(text.length / avgCharsPerToken);
  }

  /**
   * Initialize default models
   */
  private initializeModels(): void {
    const defaultModels: AIModel[] = [
      {
        id: 'gpt-4',
        name: 'GPT-4',
        provider: 'openai',
        description: 'Most capable GPT-4 model',
        capabilities: {
          maxTokens: 8192,
          supportsStreaming: true,
          supportsImages: false,
          supportsTools: true,
          supportsFunctions: true,
          contextWindow: 8192,
          costPer1kTokens: { input: 0.03, output: 0.06 }
        },
        isAvailable: true
      },
      {
        id: 'gpt-4-turbo-preview',
        name: 'GPT-4 Turbo',
        provider: 'openai',
        description: 'Latest GPT-4 Turbo model',
        capabilities: {
          maxTokens: 4096,
          supportsStreaming: true,
          supportsImages: true,
          supportsTools: true,
          supportsFunctions: true,
          contextWindow: 128000,
          costPer1kTokens: { input: 0.01, output: 0.03 }
        },
        isAvailable: true
      },
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        provider: 'openai',
        description: 'Fast and efficient GPT-3.5 model',
        capabilities: {
          maxTokens: 4096,
          supportsStreaming: true,
          supportsImages: false,
          supportsTools: true,
          supportsFunctions: true,
          contextWindow: 16385,
          costPer1kTokens: { input: 0.0005, output: 0.0015 }
        },
        isAvailable: true
      }
    ];

    defaultModels.forEach(model => this.models.set(model.id, model));
  }

  /**
   * Convert AI request to OpenAI format
   */
  private convertToOpenAIRequest(request: AIRequest): OpenAIRequest {
    return {
      model: request.options?.model || this.config.defaultModel || 'gpt-3.5-turbo',
      messages: request.messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        name: msg.name,
        tool_call_id: msg.toolCallId
      })),
      max_tokens: request.options?.maxTokens,
      temperature: request.options?.temperature,
      top_p: request.options?.topP,
      frequency_penalty: request.options?.frequencyPenalty,
      presence_penalty: request.options?.presencePenalty,
      stop: request.options?.stop,
      tools: request.options?.tools,
      tool_choice: request.options?.toolChoice,
      response_format: request.options?.responseFormat,
      seed: request.options?.seed,
      user: request.options?.user
    };
  }

  /**
   * Convert OpenAI response to AI response
   */
  private convertFromOpenAIResponse(response: OpenAIResponse, request: AIRequest): AIResponse {
    const choice = response.choices[0];
    const model = this.models.get(response.model);
    
    // Calculate cost
    const cost = this.calculateCost(response.usage, response.model);
    
    return {
      id: response.id,
      model: response.model,
      provider: 'openai',
      content: choice.message.content || '',
      finishReason: choice.finish_reason as any,
      usage: {
        promptTokens: response.usage.prompt_tokens,
        completionTokens: response.usage.completion_tokens,
        totalTokens: response.usage.total_tokens,
        cost
      },
      toolCalls: choice.message.tool_calls?.map(tc => ({
        id: tc.id,
        type: tc.type,
        function: tc.function
      })) || [],
      metadata: {
        openaiId: response.id,
        created: response.created
      },
      timestamp: new Date(),
      latency: 0 // Will be set by base class
    };
  }

  /**
   * Convert OpenAI stream chunk to AI stream chunk
   */
  private convertFromOpenAIStreamChunk(chunk: OpenAIStreamChunk): AIStreamChunk {
    const choice = chunk.choices[0];
    
    return {
      id: chunk.id,
      model: chunk.model,
      provider: 'openai',
      delta: {
        role: choice.delta.role || undefined,
        content: choice.delta.content || undefined,
        toolCalls: choice.delta.tool_calls || undefined
      },
      finishReason: choice.finish_reason,
      usage: chunk.usage ? {
        promptTokens: chunk.usage.prompt_tokens,
        completionTokens: chunk.usage.completion_tokens,
        totalTokens: chunk.usage.total_tokens
      } : undefined
    };
  }

  /**
   * Map OpenAI model to AI model
   */
  private mapOpenAIModel(openaiModel: OpenAIModel): AIModel {
    // Get capabilities from cache or use defaults
    const cached = this.models.get(openaiModel.id);
    if (cached) {
      return { ...cached, isAvailable: true };
    }

    // Default capabilities for unknown models
    const capabilities: ModelCapabilities = {
      maxTokens: 4096,
      supportsStreaming: true,
      supportsImages: false,
      supportsTools: openaiModel.id.includes('gpt-4') || openaiModel.id.includes('gpt-3.5'),
      supportsFunctions: openaiModel.id.includes('gpt-4') || openaiModel.id.includes('gpt-3.5'),
      contextWindow: openaiModel.id.includes('gpt-4') ? 8192 : 4096,
      costPer1kTokens: { input: 0.001, output: 0.002 }
    };

    return {
      id: openaiModel.id,
      name: openaiModel.id,
      provider: 'openai',
      description: `OpenAI ${openaiModel.id} model`,
      capabilities,
      isAvailable: true
    };
  }

  /**
   * Calculate cost for usage
   */
  private calculateCost(usage: { prompt_tokens: number; completion_tokens: number }, modelId: string): number {
    const model = this.models.get(modelId);
    if (!model) return 0;

    const inputCost = (usage.prompt_tokens / 1000) * model.capabilities.costPer1kTokens.input;
    const outputCost = (usage.completion_tokens / 1000) * model.capabilities.costPer1kTokens.output;
    
    return inputCost + outputCost;
  }

  /**
   * Make HTTP request to OpenAI API
   */
  private async makeHttpRequest(endpoint: string, method: string, body?: any): Promise<Response> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = this.createHeaders();

    const response = await fetch(url, {
      method,
      headers,
      body: body ? JSON.stringify(body) : null,
      signal: AbortSignal.timeout(this.config.timeout || 30000)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      this.handleHttpError(response.status, errorData.error?.message || response.statusText, errorData);
    }

    return response;
  }
}
